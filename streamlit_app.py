#!/usr/bin/env python3
"""
Judol Remover - Streamlit Application
Aplikasi untuk mendeteksi dan menghapus komentar spam/judol dari Facebook menggunakan IndoBERT
"""

import streamlit as st
import pandas as pd
import time
import json
import os
from datetime import datetime, timedelta
import requests
from typing import Dict, List, Optional
import asyncio
import threading

# Import custom modules
from python.services.spam_detector import SpamDetector
from streamlit_facebook import FacebookAPI
from streamlit_monitor import AutoMonitor

# Page configuration
st.set_page_config(
    page_title="Judol Remover Dashboard",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .spam-comment {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
        padding: 0.5rem;
        margin: 0.5rem 0;
    }
    .normal-comment {
        background-color: #e8f5e8;
        border-left: 4px solid #4caf50;
        padding: 0.5rem;
        margin: 0.5rem 0;
    }
    .stButton > button {
        width: 100%;
    }
    /* Reduce UI flicker and smooth transitions */
    .stApp {
        transition: none !important;
    }
    .element-container {
        transition: none !important;
    }
    .stMetric {
        transition: all 0.3s ease;
    }
    /* Prevent layout shift */
    .main .block-container {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    /* Custom notification styles */
    .custom-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
        animation: slideInRight 0.3s ease-out;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .custom-notification:hover {
        transform: translateX(-5px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    .notification-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .notification-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .notification-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }

    .notification-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }

    .notification-close {
        float: right;
        font-size: 18px;
        font-weight: bold;
        line-height: 1;
        color: inherit;
        opacity: 0.5;
        margin-left: 15px;
        cursor: pointer;
        transition: opacity 0.2s;
    }

    .notification-close:hover {
        opacity: 1;
    }

    .notification-icon {
        display: inline-block;
        margin-right: 8px;
        font-size: 16px;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .notification-fade-out {
        animation: slideOutRight 0.3s ease-in forwards;
    }
</style>
""", unsafe_allow_html=True)

class NotificationManager:
    """Manages custom notifications with auto-hide functionality"""

    @staticmethod
    def show_notification(message: str, notification_type: str = "success", duration: int = 5000, auto_hide: bool = True):
        """
        Show a custom notification using Streamlit's built-in components with custom styling

        Args:
            message: The notification message
            notification_type: Type of notification (success, error, warning, info)
            duration: Duration in seconds before auto-hide (default: 5)
            auto_hide: Whether to auto-hide the notification
        """
        # Initialize notifications list in session state
        if 'notifications' not in st.session_state:
            st.session_state.notifications = []

        # Create notification ID
        import uuid
        notification_id = str(uuid.uuid4())

        # Add notification to session state
        notification = {
            'id': notification_id,
            'message': message,
            'type': notification_type,
            'duration': duration,
            'auto_hide': auto_hide,
            'timestamp': time.time(),
            'show': True
        }

        st.session_state.notifications.append(notification)

        # Show notification immediately using toast (Streamlit 1.27+)
        try:
            if notification_type == "success":
                st.toast(f"{message}", icon="✅")
            elif notification_type == "error":
                st.toast(f"{message}", icon="❌")
            elif notification_type == "warning":
                st.toast(f"{message}", icon="⚠️")
            elif notification_type == "info":
                st.toast(f"{message}", icon="ℹ️")
        except AttributeError:
            # Fallback for older Streamlit versions
            NotificationManager._show_fallback_notification(message, notification_type)

    @staticmethod
    def _show_fallback_notification(message: str, notification_type: str):
        """Fallback notification for older Streamlit versions"""
        # Create a container at the top of the page
        if 'notification_container' not in st.session_state:
            st.session_state.notification_container = st.empty()

        # Style mapping
        styles = {
            'success': {'color': '#155724', 'background': '#d4edda', 'border': '#c3e6cb', 'icon': '✅'},
            'error': {'color': '#721c24', 'background': '#f8d7da', 'border': '#f5c6cb', 'icon': '❌'},
            'warning': {'color': '#856404', 'background': '#fff3cd', 'border': '#ffeaa7', 'icon': '⚠️'},
            'info': {'color': '#0c5460', 'background': '#d1ecf1', 'border': '#bee5eb', 'icon': 'ℹ️'}
        }

        style = styles.get(notification_type, styles['info'])

        # Create notification HTML
        notification_html = f"""
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            padding: 15px 20px;
            border-radius: 8px;
            background-color: {style['background']};
            border: 1px solid {style['border']};
            color: {style['color']};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideInRight 0.3s ease-out;
        ">
            <span style="margin-right: 8px; font-size: 16px;">{style['icon']}</span>
            {message}
        </div>

        <style>
        @keyframes slideInRight {{
            from {{
                transform: translateX(100%);
                opacity: 0;
            }}
            to {{
                transform: translateX(0);
                opacity: 1;
            }}
        }}
        </style>
        """

        # Show notification
        st.session_state.notification_container.markdown(notification_html, unsafe_allow_html=True)

        # Auto-hide after delay
        time.sleep(0.1)  # Small delay to ensure rendering

    @staticmethod
    def _render_notifications():
        """Render all active notifications (for compatibility)"""
        # Filter out expired notifications
        if 'notifications' not in st.session_state:
            return

        current_time = time.time()
        active_notifications = []

        for notification in st.session_state.notifications:
            if notification['auto_hide']:
                # Check if notification has expired
                elapsed_time = current_time - notification['timestamp']
                if elapsed_time < notification['duration']:
                    active_notifications.append(notification)
            else:
                active_notifications.append(notification)

        st.session_state.notifications = active_notifications

    @staticmethod
    def clear_all_notifications():
        """Clear all notifications"""
        if 'notifications' in st.session_state:
            st.session_state.notifications = []
        if 'notification_container' in st.session_state:
            st.session_state.notification_container.empty()

class StreamlitJudolRemover:
    def __init__(self):
        self.initialize_session_state()
        self.load_environment()
        self.initialize_services()
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'spam_detector' not in st.session_state:
            st.session_state.spam_detector = None
        if 'facebook_api' not in st.session_state:
            st.session_state.facebook_api = None
        if 'auto_monitor' not in st.session_state:
            st.session_state.auto_monitor = None
        if 'monitor_running' not in st.session_state:
            st.session_state.monitor_running = False
        if 'posts_cache' not in st.session_state:
            st.session_state.posts_cache = {}
        if 'comments_cache' not in st.session_state:
            st.session_state.comments_cache = {}
        if 'statistics' not in st.session_state:
            st.session_state.statistics = {
                'comments_processed': 0,
                'spam_removed': 0,
                'spam_detected': 0,
                'last_check': None,
                'start_time': None
            }
        if 'auto_delete_enabled' not in st.session_state:
            st.session_state.auto_delete_enabled = os.getenv('AUTO_DELETE_SPAM', 'true').lower() == 'true'
        if 'notifications' not in st.session_state:
            st.session_state.notifications = []
    
    def load_environment(self):
        """Load environment variables"""
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            st.warning("python-dotenv not installed. Make sure environment variables are set.")
        
        self.page_id = os.getenv('PAGE_ID')
        self.page_access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.model_path = os.getenv('MODEL_PATH', './python/models')
        self.confidence_threshold = float(os.getenv('CONFIDENCE_THRESHOLD', '0.5'))
    
    def initialize_services(self):
        """Initialize spam detector and Facebook API"""
        try:
            # Initialize spam detector
            if st.session_state.spam_detector is None:
                with st.spinner("Loading spam detection model..."):
                    st.session_state.spam_detector = SpamDetector(self.model_path)

            # Initialize Facebook API
            if st.session_state.facebook_api is None and self.page_access_token:
                st.session_state.facebook_api = FacebookAPI(
                    self.page_id,
                    self.page_access_token
                )

        except Exception as e:
            NotificationManager.show_notification(f"Error initializing services: {str(e)}", "error", 8000)
    
    def render_sidebar(self):
        """Render sidebar with navigation and controls"""
        st.sidebar.markdown("## 🛡️ Judol Remover")
        st.sidebar.markdown("---")
        
        # Navigation
        page = st.sidebar.selectbox(
            "Navigate to:",
            ["Dashboard", "Manual Check", "Pending Spam", "Test Detector", "Settings", "Logs"]
        )
        
        st.sidebar.markdown("---")
        
        # Monitor controls
        st.sidebar.markdown("### 🔄 Auto Monitor")

        # Auto Delete Toggle
        auto_delete = st.sidebar.checkbox(
            "🗑️ Auto Delete Spam",
            value=st.session_state.auto_delete_enabled,
            help="Otomatis hapus komentar yang terdeteksi spam"
        )

        if auto_delete != st.session_state.auto_delete_enabled:
            st.session_state.auto_delete_enabled = auto_delete

            # Update auto monitor configuration if it exists
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                st.session_state.auto_monitor.update_config(auto_delete_enabled=auto_delete)

            NotificationManager.show_notification(f"Auto delete {'enabled' if auto_delete else 'disabled'}", "success", 2000)

        # Auto Refresh Toggle
        auto_refresh = st.sidebar.checkbox(
            "🔄 Auto Refresh UI",
            value=st.session_state.get('auto_refresh_enabled', True),
            help="Otomatis refresh dashboard setiap 10 detik"
        )
        st.session_state.auto_refresh_enabled = auto_refresh

        if st.session_state.monitor_running:
            if st.sidebar.button("⏹️ Stop Monitor", type="secondary"):
                self.stop_monitor()

            # Show monitor status
            if st.session_state.statistics['start_time']:
                runtime = datetime.now() - st.session_state.statistics['start_time']
                st.sidebar.metric("Runtime", f"{runtime.seconds // 60}m {runtime.seconds % 60}s")

            st.sidebar.metric("Comments Processed", st.session_state.statistics['comments_processed'])
            st.sidebar.metric("Spam Detected", st.session_state.statistics['spam_detected'])
            st.sidebar.metric("Spam Removed", st.session_state.statistics['spam_removed'])
        else:
            if st.sidebar.button("▶️ Start Monitor", type="primary"):
                self.start_monitor()
        
        st.sidebar.markdown("---")
        
        # System status
        st.sidebar.markdown("### 📊 System Status")

        # Model status
        model_status = "🟢 Ready" if st.session_state.spam_detector else "🔴 Not Loaded"
        st.sidebar.markdown(f"**Model:** {model_status}")

        # Facebook API status
        fb_status = "🟢 Connected" if st.session_state.facebook_api else "🔴 Not Connected"
        st.sidebar.markdown(f"**Facebook:** {fb_status}")

        # Auto Monitor status
        if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
            monitor_status = "🟢 Initialized"
        else:
            monitor_status = "🔴 Not Initialized"
        st.sidebar.markdown(f"**Auto Monitor:** {monitor_status}")

        return page
    
    def render_dashboard(self):
        """Render main dashboard"""
        st.markdown('<h1 class="main-header">🛡️ Judol Remover Dashboard</h1>', unsafe_allow_html=True)
        
        # Metrics row
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            st.metric(
                "Comments Processed",
                st.session_state.statistics['comments_processed'],
                delta=None
            )

        with col2:
            spam_detected = st.session_state.statistics.get('spam_detected', 0)
            st.metric(
                "Spam Detected",
                spam_detected,
                delta=None
            )

        with col3:
            st.metric(
                "Spam Removed",
                st.session_state.statistics['spam_removed'],
                delta=None
            )

        with col4:
            pending_count = len(st.session_state.get('pending_spam', []))
            st.metric(
                "Pending Review",
                pending_count,
                delta=None,
                help="Spam comments waiting for manual review"
            )

        with col5:
            status = "🟢 Running" if st.session_state.monitor_running else "🔴 Stopped"
            st.metric("Monitor Status", status)

        # Show auto delete status
        auto_delete_status = "🟢 Enabled" if st.session_state.auto_delete_enabled else "🔴 Disabled"
        st.info(f"Auto Delete: {auto_delete_status}")

        # Show monitor configuration (debug info)
        if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
            try:
                monitor_config = st.session_state.auto_monitor.get_config()
                if monitor_config['auto_delete_enabled'] != st.session_state.auto_delete_enabled:
                    st.warning(f"⚠️ Configuration mismatch detected! UI: {st.session_state.auto_delete_enabled}, Monitor: {monitor_config['auto_delete_enabled']}")
                    # Sync configuration
                    st.session_state.auto_monitor.update_config(auto_delete_enabled=st.session_state.auto_delete_enabled)
            except AttributeError:
                # Method doesn't exist yet, skip debug info
                pass

        # Sync pending spam from auto monitor
        if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
            try:
                st.session_state.auto_monitor.sync_pending_spam_to_session_state()
            except Exception:
                pass

        # Show pending spam alert
        pending_count = len(st.session_state.get('pending_spam', []))
        if pending_count > 0 and not st.session_state.auto_delete_enabled:
            st.warning(f"⚠️ {pending_count} spam comments are waiting for manual review. Go to 'Pending Spam' page to review them.")

        # Debug: Show internal pending spam count
        if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
            internal_count = st.session_state.auto_monitor.get_pending_spam_count()
            if internal_count > 0:
                st.info(f"🔍 Debug: {internal_count} pending spam in internal storage, {pending_count} in session state")
                if st.button("🔄 Force Sync Pending Spam"):
                    st.session_state.auto_monitor.sync_pending_spam_to_session_state()
                    st.rerun()
        
        st.markdown("---")
        
        # Recent posts and comments
        self.render_recent_activity()
    
    def render_recent_activity(self):
        """Render recent posts and comments"""
        st.markdown("### 📝 Recent Posts & Comments")
        
        if not st.session_state.facebook_api:
            st.warning("⚠️ Facebook API not connected. Please check your access token.")
            return
        
        # Refresh button
        if st.button("🔄 Refresh"):
            st.session_state.posts_cache = {}
            st.session_state.comments_cache = {}
            st.rerun()
        
        try:
            # Get recent posts
            posts = st.session_state.facebook_api.get_recent_posts(limit=5)
            
            if not posts:
                st.info("No recent posts found.")
                return
            
            # Display posts with collapsible comments
            for post in posts:
                with st.expander(f"📄 Post from {post.get('created_time', 'Unknown time')}", expanded=False):
                    # Post content
                    if post.get('message'):
                        st.markdown(f"**Content:** {post['message'][:200]}...")
                    
                    # Load comments for this post
                    self.render_post_comments(post['id'])
                    
        except Exception as e:
            st.error(f"❌ Error loading posts: {str(e)}")
    
    def render_post_comments(self, post_id: str):
        """Render comments for a specific post"""
        try:
            # Get comments
            comments = st.session_state.facebook_api.get_post_comments(post_id, limit=10)
            
            if not comments:
                st.info("No comments found for this post.")
                return
            
            st.markdown(f"**Comments ({len(comments)}):**")
            
            # Process each comment
            for comment in comments:
                self.render_comment_card(comment, post_id)
                
        except Exception as e:
            st.error(f"❌ Error loading comments: {str(e)}")
    
    def render_comment_card(self, comment: Dict, post_id: str):
        """Render individual comment with spam detection"""
        comment_id = comment['id']
        message = comment.get('message', '')
        author = comment.get('from', {}).get('name', 'Unknown')
        created_time = comment.get('created_time', '')
        
        # Get spam prediction
        try:
            prediction = st.session_state.spam_detector.predict(message)
            is_spam = prediction['is_spam'] and prediction['confidence'] > self.confidence_threshold
        except Exception as e:
            prediction = {'is_spam': False, 'confidence': 0.0, 'label': 'error', 'error': str(e)}
            is_spam = False
        
        # Style based on spam detection
        card_class = "spam-comment" if is_spam else "normal-comment"
        emoji = "🚨" if is_spam else "✅"
        
        # Create comment card
        with st.container():
            st.markdown(f"""
            <div class="{card_class}">
                <strong>{emoji} {author}</strong> - {created_time}<br>
                <em>"{message[:100]}{'...' if len(message) > 100 else ''}"</em><br>
                <small>Confidence: {prediction['confidence']:.3f} | Label: {prediction['label']}</small>
            </div>
            """, unsafe_allow_html=True)
            
            # Action buttons
            col1, col2 = st.columns([1, 1])

            with col1:
                # Show delete button for spam comments or allow manual delete for any comment
                if is_spam:
                    if st.session_state.auto_delete_enabled:
                        st.info("🤖 Auto-deleted")
                    else:
                        if st.button(f"🗑️ Delete Spam", key=f"delete_{comment_id}", type="primary"):
                            self.delete_comment(comment_id, post_id, message, author, reason="Manual spam deletion")
                else:
                    if st.button(f"🗑️ Delete", key=f"delete_{comment_id}", help="Manual moderation"):
                        self.delete_comment(comment_id, post_id, message, author, reason="Manual moderation")

            with col2:
                if st.button(f"🔍 Details", key=f"details_{comment_id}"):
                    self.show_comment_details(comment, prediction)

    def start_monitor(self):
        """Start auto monitoring"""
        try:
            # Check prerequisites
            if not st.session_state.facebook_api:
                st.error("❌ Facebook API not connected. Please configure in Settings.")
                return

            if not st.session_state.spam_detector:
                st.error("❌ Spam detector not loaded. Please check model files.")
                return

            # Initialize auto monitor if not exists or if it's None
            if 'auto_monitor' not in st.session_state or st.session_state.auto_monitor is None:
                from streamlit_monitor import AutoMonitor
                st.session_state.auto_monitor = AutoMonitor(
                    st.session_state.facebook_api,
                    st.session_state.spam_detector,
                    poll_interval=30
                )

            # Update monitor configuration with current settings
            if st.session_state.auto_monitor:
                st.session_state.auto_monitor.update_config(
                    auto_delete_enabled=st.session_state.auto_delete_enabled,
                    confidence_threshold=self.confidence_threshold
                )

                # Start the monitor
                st.session_state.auto_monitor.start()
                st.session_state.monitor_running = True
                st.session_state.statistics['start_time'] = datetime.now()
                NotificationManager.show_notification("Auto monitor started!", "success", 3000)
            else:
                NotificationManager.show_notification("Failed to initialize auto monitor", "error", 5000)

            st.rerun()
        except Exception as e:
            st.error(f"❌ Error starting monitor: {str(e)}")
            # Reset auto monitor on error
            st.session_state.auto_monitor = None
    
    def stop_monitor(self):
        """Stop auto monitoring"""
        try:
            # Stop the auto monitor if it exists and is not None
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                st.session_state.auto_monitor.stop()

            st.session_state.monitor_running = False
            st.session_state.statistics['start_time'] = None
            NotificationManager.show_notification("Auto monitor stopped!", "info", 3000)
            st.rerun()
        except Exception as e:
            NotificationManager.show_notification(f"Error stopping monitor: {str(e)}", "error", 5000)
            # Reset monitor state on error
            st.session_state.monitor_running = False
    
    def delete_comment(self, comment_id: str, post_id: str, message: str, author: str, reason: str = "Manual deletion"):
        """Delete a comment"""
        try:
            success = st.session_state.facebook_api.delete_comment(comment_id)
            if success:
                st.session_state.statistics['spam_removed'] += 1
                NotificationManager.show_notification(f"Deleted comment by {author} ({reason})", "success", 4000)

                # Log the deletion
                if 'monitor_logs' not in st.session_state:
                    st.session_state.monitor_logs = []

                log_entry = {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'DELETED',
                    'comment_id': comment_id,
                    'author': author,
                    'message': message[:100],
                    'post_id': post_id,
                    'reason': reason
                }
                st.session_state.monitor_logs.append(log_entry)

                # Clear cache to refresh comments
                if post_id in st.session_state.comments_cache:
                    del st.session_state.comments_cache[post_id]
                st.rerun()
            else:
                NotificationManager.show_notification("Failed to delete comment", "error", 5000)
        except Exception as e:
            NotificationManager.show_notification(f"Error deleting comment: {str(e)}", "error", 5000)
    
    def show_comment_details(self, comment: Dict, prediction: Dict):
        """Show detailed comment information"""
        st.json({
            "comment": comment,
            "prediction": prediction
        })
    
    def run(self):
        """Main application runner"""
        # Clean up expired notifications
        NotificationManager._render_notifications()

        # Render sidebar and get current page
        current_page = self.render_sidebar()

        # Render main content based on selected page
        if current_page == "Dashboard":
            self.render_dashboard()
        elif current_page == "Manual Check":
            self.render_manual_check()
        elif current_page == "Pending Spam":
            self.render_pending_spam()
        elif current_page == "Test Detector":
            self.render_test_detector()
        elif current_page == "Settings":
            self.render_settings()
        elif current_page == "Logs":
            self.render_logs()
        
        # Auto-refresh for real-time updates (only on dashboard and if enabled)
        if (st.session_state.monitor_running and
            current_page == "Dashboard" and
            st.session_state.get('auto_refresh_enabled', True)):

            # Update statistics and sync pending spam from auto monitor
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                try:
                    # Update statistics
                    monitor_stats = st.session_state.auto_monitor.get_statistics()
                    if monitor_stats:
                        st.session_state.statistics.update(monitor_stats)

                    # Sync pending spam from internal storage to session state
                    st.session_state.auto_monitor.sync_pending_spam_to_session_state()

                except Exception:
                    # Log error but don't stop monitoring
                    pass

            # Only refresh every 10 seconds instead of 5
            time.sleep(10)
            st.rerun()

        # Always sync pending spam when monitor is running (even on other pages)
        elif st.session_state.monitor_running:
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                try:
                    st.session_state.auto_monitor.sync_pending_spam_to_session_state()
                except Exception:
                    pass

    def render_manual_check(self):
        """Render manual check page"""
        st.markdown("### 🔍 Manual Comment Check")

        if not st.session_state.facebook_api:
            st.warning("⚠️ Facebook API not connected.")
            return

        # Post selection
        st.markdown("#### Select Post to Check")

        try:
            posts = st.session_state.facebook_api.get_recent_posts(limit=10)

            if not posts:
                st.info("No posts found.")
                return

            # Create post options
            post_options = {}
            for post in posts:
                preview = post.get('message', 'No message')[:50] + "..."
                created_time = post.get('created_time', 'Unknown time')
                option_text = f"{created_time} - {preview}"
                post_options[option_text] = post['id']

            selected_post_text = st.selectbox("Choose a post:", list(post_options.keys()))
            selected_post_id = post_options[selected_post_text]

            col1, col2 = st.columns([1, 4])

            with col1:
                if st.button("🔍 Check Post", type="primary"):
                    with st.spinner("Checking post for spam comments..."):
                        if 'auto_monitor' not in st.session_state:
                            from streamlit_monitor import AutoMonitor
                            st.session_state.auto_monitor = AutoMonitor(
                                st.session_state.facebook_api,
                                st.session_state.spam_detector
                            )

                        results = st.session_state.auto_monitor.manual_check_post(selected_post_id)
                        st.session_state.manual_check_results = results

            # Display results
            if 'manual_check_results' in st.session_state:
                results = st.session_state.manual_check_results

                st.markdown("#### 📊 Check Results")

                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Comments Checked", results['comments_checked'])
                with col2:
                    st.metric("Spam Found", results['spam_found'])
                with col3:
                    st.metric("Spam Removed", results['spam_removed'])
                with col4:
                    st.metric("Errors", results['errors'])

                # Detailed results
                if results.get('details'):
                    st.markdown("#### 📝 Detailed Results")

                    for detail in results['details']:
                        emoji = "🚨" if detail['is_spam'] else "✅"
                        status = "DELETED" if detail.get('deleted') else ("SPAM" if detail['is_spam'] else "NORMAL")

                        with st.expander(f"{emoji} {detail['author']} - {status}"):
                            st.write(f"**Message:** {detail['message']}")
                            st.write(f"**Confidence:** {detail['confidence']:.3f}")
                            st.write(f"**Comment ID:** {detail['comment_id']}")
                            if detail.get('deleted'):
                                st.success("✅ Comment deleted successfully")

        except Exception as e:
            st.error(f"❌ Error in manual check: {str(e)}")

    def render_pending_spam(self):
        """Render pending spam page for manual review"""
        st.markdown("### 🚨 Pending Spam Comments")
        st.markdown("Komentar yang terdeteksi sebagai spam tapi belum dihapus karena auto-delete dinonaktifkan.")

        # Initialize pending spam if not exists
        if 'pending_spam' not in st.session_state:
            st.session_state.pending_spam = []

        pending_comments = st.session_state.pending_spam

        if not pending_comments:
            st.info("📭 Tidak ada komentar spam yang menunggu review.")
            if not st.session_state.auto_delete_enabled:
                st.warning("💡 Auto-delete sedang dinonaktifkan. Aktifkan di sidebar untuk menghapus spam secara otomatis.")
            return

        st.markdown(f"#### 📊 {len(pending_comments)} komentar menunggu review")

        # Bulk actions
        col1, col2 = st.columns([1, 1])

        with col1:
            if st.button("🗑️ Delete All Spam", type="primary"):
                deleted_count = 0
                for comment in pending_comments[:]:  # Copy list to avoid modification during iteration
                    try:
                        success = st.session_state.facebook_api.delete_comment(comment['comment_id'])
                        if success:
                            deleted_count += 1
                            st.session_state.pending_spam.remove(comment)

                            # Log the deletion
                            self._log_deletion(comment, "Bulk manual deletion")

                    except Exception as e:
                        st.error(f"Failed to delete comment {comment['comment_id']}: {str(e)}")

                NotificationManager.show_notification(f"Deleted {deleted_count} spam comments", "success", 4000)
                st.rerun()

        with col2:
            if st.button("✅ Mark All as Normal"):
                st.session_state.pending_spam = []
                NotificationManager.show_notification("All comments marked as normal", "info", 3000)
                st.rerun()

        # Display pending comments
        for i, comment in enumerate(pending_comments):
            with st.expander(f"🚨 Spam #{i+1} - {comment['author']}", expanded=False):
                st.markdown(f"**Author:** {comment['author']}")
                st.markdown(f"**Message:** {comment['message']}")
                st.markdown(f"**Confidence:** {comment['prediction']['confidence']:.3f}")
                st.markdown(f"**Detected:** {comment['detected_time']}")

                # Individual actions
                col1, col2 = st.columns([1, 1])

                with col1:
                    if st.button(f"🗑️ Delete", key=f"delete_pending_{i}"):
                        try:
                            success = st.session_state.facebook_api.delete_comment(comment['comment_id'])
                            if success:
                                st.session_state.pending_spam.remove(comment)
                                self._log_deletion(comment, "Manual deletion from pending")
                                NotificationManager.show_notification("Comment deleted", "success", 3000)
                                st.rerun()
                            else:
                                st.error("❌ Failed to delete comment")
                        except Exception as e:
                            st.error(f"❌ Error: {str(e)}")

                with col2:
                    if st.button(f"✅ Mark Normal", key=f"normal_pending_{i}"):
                        st.session_state.pending_spam.remove(comment)
                        NotificationManager.show_notification("Marked as normal", "info", 2000)
                        st.rerun()

                # Show prediction details
                st.json(comment['prediction'])

    def _log_deletion(self, comment: Dict, reason: str):
        """Helper function to log comment deletion"""
        if 'monitor_logs' not in st.session_state:
            st.session_state.monitor_logs = []

        log_entry = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'action': 'DELETED',
            'comment_id': comment['comment_id'],
            'author': comment['author'],
            'message': comment['message'][:100],
            'post_id': comment['post_id'],
            'reason': reason
        }
        st.session_state.monitor_logs.append(log_entry)

        # Update statistics
        st.session_state.statistics['spam_removed'] += 1

    def render_test_detector(self):
        """Render spam detector test page"""
        st.markdown("### 🧪 Test Spam Detector")

        if not st.session_state.spam_detector:
            st.warning("⚠️ Spam detector not loaded.")
            return

        # Test input
        st.markdown("#### Enter text to test:")
        test_text = st.text_area(
            "Test Text",
            placeholder="Enter comment text to test for spam detection...",
            height=100
        )

        # Confidence threshold
        confidence_threshold = st.slider(
            "Confidence Threshold",
            min_value=0.0,
            max_value=1.0,
            value=self.confidence_threshold,
            step=0.05,
            help="Minimum confidence required to classify as spam"
        )

        if st.button("🔍 Test Detection", type="primary"):
            if test_text.strip():
                with st.spinner("Analyzing text..."):
                    try:
                        prediction = st.session_state.spam_detector.predict(test_text)

                        # Display results
                        st.markdown("#### 📊 Detection Results")

                        col1, col2, col3 = st.columns(3)

                        with col1:
                            label = prediction['label']
                            emoji = "🚨" if prediction['is_spam'] else "✅"
                            st.metric("Classification", f"{emoji} {label.upper()}")

                        with col2:
                            confidence = prediction['confidence']
                            st.metric("Confidence", f"{confidence:.3f}")

                        with col3:
                            is_spam_threshold = prediction['is_spam'] and confidence > confidence_threshold
                            action = "DELETE" if is_spam_threshold else "KEEP"
                            color = "🔴" if is_spam_threshold else "🟢"
                            st.metric("Action", f"{color} {action}")

                        # Detailed information
                        st.markdown("#### 📋 Detailed Information")
                        st.json(prediction)

                        # Explanation
                        if prediction['is_spam']:
                            if confidence > confidence_threshold:
                                st.error(f"🚨 This comment would be **DELETED** (confidence {confidence:.3f} > threshold {confidence_threshold})")
                            else:
                                st.warning(f"⚠️ Detected as spam but confidence {confidence:.3f} is below threshold {confidence_threshold}")
                        else:
                            st.success("✅ This comment would be **KEPT** (classified as normal)")

                    except Exception as e:
                        st.error(f"❌ Error testing detection: {str(e)}")
            else:
                st.warning("Please enter some text to test.")

        # Batch testing
        st.markdown("---")
        st.markdown("#### 📝 Batch Testing")

        batch_text = st.text_area(
            "Batch Test (one comment per line)",
            placeholder="Enter multiple comments, one per line...",
            height=150
        )

        if st.button("🔍 Test Batch", type="secondary"):
            if batch_text.strip():
                lines = [line.strip() for line in batch_text.split('\n') if line.strip()]

                if lines:
                    with st.spinner(f"Testing {len(lines)} comments..."):
                        results = []

                        for line in lines:
                            try:
                                prediction = st.session_state.spam_detector.predict(line)
                                results.append({
                                    'text': line[:50] + "..." if len(line) > 50 else line,
                                    'full_text': line,
                                    'is_spam': prediction['is_spam'],
                                    'confidence': prediction['confidence'],
                                    'label': prediction['label'],
                                    'action': 'DELETE' if prediction['is_spam'] and prediction['confidence'] > confidence_threshold else 'KEEP'
                                })
                            except Exception as e:
                                results.append({
                                    'text': line[:50] + "..." if len(line) > 50 else line,
                                    'full_text': line,
                                    'is_spam': False,
                                    'confidence': 0.0,
                                    'label': 'error',
                                    'action': 'ERROR',
                                    'error': str(e)
                                })

                        # Display results table
                        df = pd.DataFrame(results)
                        st.dataframe(
                            df[['text', 'label', 'confidence', 'action']],
                            use_container_width=True
                        )

                        # Summary
                        spam_count = sum(1 for r in results if r['is_spam'] and r['confidence'] > confidence_threshold)
                        st.info(f"📊 Summary: {spam_count} out of {len(results)} comments would be deleted as spam")

    def render_settings(self):
        """Render settings page"""
        st.markdown("### ⚙️ Settings")

        # Facebook API Settings
        st.markdown("#### 📘 Facebook API Configuration")

        col1, col2 = st.columns(2)

        with col1:
            page_id = st.text_input("Page ID", value=self.page_id or "", help="Facebook Page ID")

        with col2:
            page_access_token = st.text_input(
                "Page Access Token",
                value=self.page_access_token or "",
                type="password",
                help="Facebook Page Access Token"
            )

        if st.button("💾 Update Facebook Settings"):
            if page_id and page_access_token:
                try:
                    # Test new credentials
                    from streamlit_facebook import FacebookAPI
                    test_api = FacebookAPI(page_id, page_access_token)

                    # Update session state
                    st.session_state.facebook_api = test_api
                    self.page_id = page_id
                    self.page_access_token = page_access_token

                    NotificationManager.show_notification("Facebook API settings updated successfully!", "success", 4000)
                except Exception as e:
                    NotificationManager.show_notification(f"Failed to update Facebook settings: {str(e)}", "error", 6000)
            else:
                st.warning("Please provide both Page ID and Access Token")

        st.markdown("---")

        # Spam Detection Settings
        st.markdown("#### 🤖 Spam Detection Configuration")

        confidence_threshold = st.slider(
            "Confidence Threshold",
            min_value=0.0,
            max_value=1.0,
            value=self.confidence_threshold,
            step=0.05,
            help="Minimum confidence required to classify and delete as spam"
        )

        model_path = st.text_input(
            "Model Path",
            value=self.model_path,
            help="Path to the IndoBERT model directory"
        )

        if st.button("💾 Update Detection Settings"):
            self.confidence_threshold = confidence_threshold
            st.session_state.confidence_threshold = confidence_threshold

            if model_path != self.model_path:
                try:
                    # Reload model with new path
                    with st.spinner("Reloading spam detection model..."):
                        st.session_state.spam_detector = SpamDetector(model_path)
                    self.model_path = model_path
                    NotificationManager.show_notification("Model reloaded successfully!", "success", 4000)
                except Exception as e:
                    NotificationManager.show_notification(f"Failed to reload model: {str(e)}", "error", 6000)
            else:
                NotificationManager.show_notification("Detection settings updated!", "success", 3000)

        st.markdown("---")

        # Monitor Settings
        st.markdown("#### 🔄 Auto Monitor Configuration")

        poll_interval = st.number_input(
            "Poll Interval (seconds)",
            min_value=10,
            max_value=300,
            value=30,
            step=5,
            help="How often to check for new comments"
        )

        auto_delete = st.checkbox(
            "Auto Delete Spam",
            value=True,
            help="Automatically delete comments detected as spam"
        )

        if st.button("💾 Update Monitor Settings"):
            st.session_state.poll_interval = poll_interval
            st.session_state.auto_delete = auto_delete
            NotificationManager.show_notification("Monitor settings updated!", "success", 3000)

        st.markdown("---")

        # System Information
        st.markdown("#### 📊 System Information")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Model Status:**")
            if st.session_state.spam_detector:
                st.success("🟢 Loaded")
                st.write(f"Model Path: `{self.model_path}`")
            else:
                st.error("🔴 Not Loaded")

        with col2:
            st.markdown("**Facebook API Status:**")
            if st.session_state.facebook_api:
                st.success("🟢 Connected")
                st.write(f"Page ID: `{self.page_id}`")
            else:
                st.error("🔴 Not Connected")

        # Cache Management
        st.markdown("---")
        st.markdown("#### 🗂️ Cache Management")

        col1, col2, col3 = st.columns(3)

        with col1:
            posts_cache_size = len(st.session_state.posts_cache)
            st.metric("Posts Cache", posts_cache_size)

        with col2:
            comments_cache_size = len(st.session_state.comments_cache)
            st.metric("Comments Cache", comments_cache_size)

        with col3:
            if st.button("🗑️ Clear Cache"):
                st.session_state.posts_cache = {}
                st.session_state.comments_cache = {}
                NotificationManager.show_notification("Cache cleared!", "info", 2000)
                st.rerun()

    def render_logs(self):
        """Render logs page"""
        st.markdown("### 📋 Activity Logs")

        # Initialize logs if not exists
        if 'monitor_logs' not in st.session_state:
            st.session_state.monitor_logs = []

        # Log controls
        col1, col2 = st.columns([1, 1])

        with col1:
            if st.button("🔄 Refresh Logs"):
                st.rerun()

        with col2:
            if st.button("🗑️ Clear Logs"):
                st.session_state.monitor_logs = []
                NotificationManager.show_notification("Logs cleared!", "info", 2000)
                st.rerun()

        # Display logs
        logs = st.session_state.monitor_logs

        if not logs:
            st.info("📭 No activity logs yet. Start the auto monitor to see activity.")
            return

        st.markdown(f"#### Recent Activity ({len(logs)} entries)")

        # Filter options
        col1, col2 = st.columns(2)

        with col1:
            log_filter = st.selectbox(
                "Filter by action:",
                ["All", "DELETED", "ERROR", "INFO"]
            )

        with col2:
            show_count = st.number_input(
                "Show last N entries:",
                min_value=10,
                max_value=100,
                value=50,
                step=10
            )

        # Filter and display logs
        filtered_logs = logs
        if log_filter != "All":
            filtered_logs = [log for log in logs if log.get('action') == log_filter]

        # Show most recent entries
        recent_logs = filtered_logs[-show_count:]
        recent_logs.reverse()  # Show newest first

        # Display logs in a table format
        if recent_logs:
            log_data = []
            for log in recent_logs:
                log_data.append({
                    'Timestamp': log.get('timestamp', ''),
                    'Action': log.get('action', ''),
                    'Author': log.get('author', ''),
                    'Message': log.get('message', '')[:50] + "..." if len(log.get('message', '')) > 50 else log.get('message', ''),
                    'Comment ID': log.get('comment_id', '')
                })

            df = pd.DataFrame(log_data)
            st.dataframe(df, use_container_width=True)

            # Detailed view
            st.markdown("#### 📝 Detailed Log Entries")

            for log in recent_logs[:10]:  # Show details for first 10
                with st.expander(f"{log.get('timestamp', '')} - {log.get('action', '')}"):
                    st.json(log)
        else:
            st.info(f"No logs found for filter: {log_filter}")

def main():
    """Main function"""
    app = StreamlitJudolRemover()
    app.run()

if __name__ == "__main__":
    main()
